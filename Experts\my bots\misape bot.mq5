#property copyright "Misape Smart Bot"
#property link      "https://www.mql5.com"
#property version   "9.11"
#property strict

// Macro definitions
#define INPUT_NEURONS 12
#define HIDDEN_NEURONS 10
#define OUTPUT_NEURONS 3

// Input parameters
input int FastMAPeriod = 10;        // Fast MA Period
input int SlowMAPeriod = 20;        // Slow MA Period
input double LotSize = 0.1;         // Lot Size
input int StopLoss = 50;            // Stop Loss (in pips)
input int TakeProfit = 100;         // Take Profit (in pips)
input int TrailingStop = 20;        // Trailing Stop (in pips)
input int MagicNumber = 123456;     // Magic Number
input bool SimplifiedTrading = true; // Use simplified trading conditions (recommended for testing)
input int RSIPeriod1 = 2;           // RSI Period 1
input int RSIPeriod2 = 6;           // RSI Period 2
input int RSIPeriod3 = 10;          // RSI Period 3
input int RSIPeriod4 = 14;          // RSI Period 4
input int RSIPeriod5 = 18;          // RSI Period 5
input int RSIPeriod6 = 22;          // RSI Period 6
input int MACDFast = 12;            // MACD Fast Period
input int MACDSlow = 26;            // MACD Slow Period
input int MACDSignal = 9;           // MACD Signal Period
input int BBPeriod = 20;            // Bollinger Bands Period
input double BBDeviation = 2.0;     // Bollinger Bands Deviation
input int TMAPeriod = 20;           // TMA Period
input int ATRPeriod = 14;           // ATR Period for TMA/VWAP
input int OBVPeriod = 20;           // OBV Lookback Period
input int VWAPPeriod = 20;          // VWAP Period
input int StochasticPeriod1 = 5;    // Stochastic Period 1
input int StochasticPeriod2 = 10;   // Stochastic Period 2
input int StochasticPeriod3 = 14;   // Stochastic Period 3
input int StochasticK = 3;          // Stochastic K Period
input int StochasticD = 3;          // Stochastic D Period
input int StochasticSlowing = 3;    // Stochastic Slowing
input int DivergenceBars = 20;      // Bars for Divergence
input ENUM_TIMEFRAMES MTF1 = PERIOD_H1; // First MTF Timeframe
input ENUM_TIMEFRAMES MTF2 = PERIOD_H4; // Second MTF Timeframe
input int MTF_FastEMAPeriod = 9;    // MTF Fast EMA Period
input int MTF_SlowEMAPeriod = 21;   // MTF Slow EMA Period
input double WeightRSI = 0.25;      // Weight: RSI
input double WeightMACD = 0.25;     // Weight: MACD
input double WeightBB = 0.15;       // Weight: Bollinger Bands
input double WeightOBV = 0.15;      // Weight: OBV
input double WeightVWAP = 0.15;     // Weight: VWAP
input double WeightStochastic = 0.05; // Weight: Stochastic
input double SignalThresholdBase = 0.3; // Base Signal Threshold (lowered for easier trading)
input int MarkovBars = 500;         // Bars for Markov Model
input double MarkovBullish = 0.01;  // Price Change for Bullish State
input double MarkovBearish = -0.01; // Price Change for Bearish State
input double MarkovProbThreshold = 0.5; // Markov Probability Threshold (lowered for easier trading)
input bool UseNeuralNetwork = true; // Use Neural Network
input int NNTrainingBars = 5000;    // Bars for NN Training
input double NNLearningRate = 0.01; // NN Learning Rate
input int NNTrainingEpochs = 50;    // NN Training Epochs
input int NNUpdateBars = 100;       // Bars Between NN Updates

// Global variables
int fastMAHandle, slowMAHandle, atrHandle, tmaHandle;
int rsiHandle1, rsiHandle2, rsiHandle3, rsiHandle4, rsiHandle5, rsiHandle6;
int macdHandle, bbHandle, obvHandle, vwapHandle;
int stochasticHandle1, stochasticHandle2, stochasticHandle3;
int mtf1FastEMAHandle, mtf1SlowEMAHandle, mtf2FastEMAHandle, mtf2SlowEMAHandle;
double fastMA[], slowMA[], rsi1[], rsi2[], rsi3[], rsi4[], rsi5[], rsi6[];
double macd[], bbUpper[], bbLower[], obv[], vwap[], stochastic1[], stochastic2[], stochastic3[];
double tma[], tmaUpper[], tmaLower[], atr[];
double mtf1FastEMA[], mtf1SlowEMA[], mtf2FastEMA[], mtf2SlowEMA[];
double lastFastMA, lastSlowMA, prevFastMA, prevSlowMA;
enum MARKET_STATE {BULLISH, BEARISH, NEUTRAL};
MARKET_STATE currentState;
double markovMatrix[3][3];
bool tradingEnabled = true;
double totalProfit = 0;
int totalTrades = 0;
int winningTrades = 0;
datetime lastBarTime;
int barsSinceLastNNUpdate;

// Neural network parameters
double nnWeights1[INPUT_NEURONS][HIDDEN_NEURONS];
double nnWeights2[HIDDEN_NEURONS][HIDDEN_NEURONS];
double nnWeights3[HIDDEN_NEURONS][OUTPUT_NEURONS];
double nnBias1[HIDDEN_NEURONS];
double nnBias2[HIDDEN_NEURONS];
double nnBias3[OUTPUT_NEURONS];
double nnOutputs[OUTPUT_NEURONS];

//+------------------------------------------------------------------+
//| Expert initialization function                                     |
//+------------------------------------------------------------------+
int OnInit()
{
   // Validate input parameters
   if(FastMAPeriod <= 0 || SlowMAPeriod <= 0 || LotSize <= 0 || StopLoss <= 0 || TakeProfit <= 0 || TrailingStop <= 0 ||
      TMAPeriod <= 0 || ATRPeriod <= 0 || OBVPeriod <= 0 || VWAPPeriod <= 0 || DivergenceBars <= 0 ||
      MTF_FastEMAPeriod <= 0 || MTF_SlowEMAPeriod <= 0 || MarkovBars <= 0 || NNTrainingBars <= 0 ||
      RSIPeriod1 <= 0 || RSIPeriod2 <= 0 || RSIPeriod3 <= 0 || RSIPeriod4 <= 0 || RSIPeriod5 <= 0 || RSIPeriod6 <= 0 ||
      MACDFast <= 0 || MACDSlow <= 0 || MACDSignal <= 0 || StochasticPeriod1 <= 0 || StochasticPeriod2 <= 0 || StochasticPeriod3 <= 0)
   {
      Print("Invalid input parameters");
      return(INIT_PARAMETERS_INCORRECT);
   }

   // Check for Deriv broker only in live trading
   if(!MQLInfoInteger(MQL_TESTER)) // Skip check if running in tester
   {
      string company = TerminalInfoString(TERMINAL_COMPANY);
      if(StringFind(company, "Deriv", 0) < 0)
      {
         Print("Broker is not Deriv. Found: ", company);
         return(INIT_FAILED);
      }
   }

   // Initialize indicators
   fastMAHandle = iMA(_Symbol, _Period, FastMAPeriod, 0, MODE_SMA, PRICE_CLOSE);
   slowMAHandle = iMA(_Symbol, _Period, SlowMAPeriod, 0, MODE_SMA, PRICE_CLOSE);
   atrHandle = iATR(_Symbol, _Period, ATRPeriod);
   // Note: TMA and VWAP will be calculated in OnTick when sufficient data is available
   tmaHandle = 1; // Pseudo-handle for custom TMA
   rsiHandle1 = iRSI(_Symbol, _Period, RSIPeriod1, PRICE_CLOSE);
   rsiHandle2 = iRSI(_Symbol, _Period, RSIPeriod2, PRICE_CLOSE);
   rsiHandle3 = iRSI(_Symbol, _Period, RSIPeriod3, PRICE_CLOSE);
   rsiHandle4 = iRSI(_Symbol, _Period, RSIPeriod4, PRICE_CLOSE);
   rsiHandle5 = iRSI(_Symbol, _Period, RSIPeriod5, PRICE_CLOSE);
   rsiHandle6 = iRSI(_Symbol, _Period, RSIPeriod6, PRICE_CLOSE);
   macdHandle = iMACD(_Symbol, _Period, MACDFast, MACDSlow, MACDSignal, PRICE_CLOSE);
   bbHandle = iBands(_Symbol, _Period, BBPeriod, 0, BBDeviation, PRICE_CLOSE);
   obvHandle = iOBV(_Symbol, _Period, VOLUME_TICK);
   vwapHandle = 1; // Pseudo-handle for custom VWAP
   stochasticHandle1 = iStochastic(_Symbol, _Period, StochasticPeriod1, StochasticK, StochasticD, MODE_SMA, STO_LOWHIGH);
   stochasticHandle2 = iStochastic(_Symbol, _Period, StochasticPeriod2, StochasticK, StochasticD, MODE_SMA, STO_LOWHIGH);
   stochasticHandle3 = iStochastic(_Symbol, _Period, StochasticPeriod3, StochasticK, StochasticD, MODE_SMA, STO_LOWHIGH);
   mtf1FastEMAHandle = iMA(_Symbol, MTF1, MTF_FastEMAPeriod, 0, MODE_EMA, PRICE_CLOSE);
   mtf1SlowEMAHandle = iMA(_Symbol, MTF1, MTF_SlowEMAPeriod, 0, MODE_EMA, PRICE_CLOSE);
   mtf2FastEMAHandle = iMA(_Symbol, MTF2, MTF_FastEMAPeriod, 0, MODE_EMA, PRICE_CLOSE);
   mtf2SlowEMAHandle = iMA(_Symbol, MTF2, MTF_SlowEMAPeriod, 0, MODE_EMA, PRICE_CLOSE);

   // Check each indicator individually for better error reporting
   if(fastMAHandle == INVALID_HANDLE)
   {
      Print("Failed to create Fast MA indicator");
      return(INIT_FAILED);
   }
   if(slowMAHandle == INVALID_HANDLE)
   {
      Print("Failed to create Slow MA indicator");
      return(INIT_FAILED);
   }
   if(atrHandle == INVALID_HANDLE)
   {
      Print("Failed to create ATR indicator");
      return(INIT_FAILED);
   }
   if(rsiHandle1 == INVALID_HANDLE || rsiHandle2 == INVALID_HANDLE || rsiHandle3 == INVALID_HANDLE ||
      rsiHandle4 == INVALID_HANDLE || rsiHandle5 == INVALID_HANDLE || rsiHandle6 == INVALID_HANDLE)
   {
      Print("Failed to create one or more RSI indicators");
      return(INIT_FAILED);
   }
   if(macdHandle == INVALID_HANDLE)
   {
      Print("Failed to create MACD indicator");
      return(INIT_FAILED);
   }
   if(bbHandle == INVALID_HANDLE)
   {
      Print("Failed to create Bollinger Bands indicator");
      return(INIT_FAILED);
   }
   if(obvHandle == INVALID_HANDLE)
   {
      Print("Failed to create OBV indicator");
      return(INIT_FAILED);
   }
   if(stochasticHandle1 == INVALID_HANDLE || stochasticHandle2 == INVALID_HANDLE || stochasticHandle3 == INVALID_HANDLE)
   {
      Print("Failed to create one or more Stochastic indicators");
      return(INIT_FAILED);
   }
   if(mtf1FastEMAHandle == INVALID_HANDLE || mtf1SlowEMAHandle == INVALID_HANDLE ||
      mtf2FastEMAHandle == INVALID_HANDLE || mtf2SlowEMAHandle == INVALID_HANDLE)
   {
      Print("Failed to create Multi-Timeframe EMA indicators");
      return(INIT_FAILED);
   }

   ArraySetAsSeries(fastMA, true);
   ArraySetAsSeries(slowMA, true);
   ArraySetAsSeries(atr, true);
   ArraySetAsSeries(tma, true);
   ArraySetAsSeries(tmaUpper, true);
   ArraySetAsSeries(tmaLower, true);
   ArraySetAsSeries(rsi1, true);
   ArraySetAsSeries(rsi2, true);
   ArraySetAsSeries(rsi3, true);
   ArraySetAsSeries(rsi4, true);
   ArraySetAsSeries(rsi5, true);
   ArraySetAsSeries(rsi6, true);
   ArraySetAsSeries(macd, true);
   ArraySetAsSeries(bbUpper, true);
   ArraySetAsSeries(bbLower, true);
   ArraySetAsSeries(obv, true);
   ArraySetAsSeries(vwap, true);
   ArraySetAsSeries(stochastic1, true);
   ArraySetAsSeries(stochastic2, true);
   ArraySetAsSeries(stochastic3, true);
   ArraySetAsSeries(mtf1FastEMA, true);
   ArraySetAsSeries(mtf1SlowEMA, true);
   ArraySetAsSeries(mtf2FastEMA, true);
   ArraySetAsSeries(mtf2SlowEMA, true);

   // Initialize Markov matrix
   ArrayInitialize(markovMatrix, 0.33);
   UpdateMarkovMatrix();

   // Initialize neural network
   if(UseNeuralNetwork)
   {
      InitializeNeuralNetwork();
      if(!TrainNeuralNetwork())
      {
         Print("Neural network training failed");
         return(INIT_FAILED);
      }
   }

   // Initialize all arrays to prevent "array out of range" errors
   InitializeArrays();

   // Initialize dashboard
   CreateDashboard();
   lastBarTime = TimeCurrent();
   barsSinceLastNNUpdate = 0;

   Print("Misape Smart Bot initialized successfully - All indicators created");
   Print("TMA and VWAP will be calculated dynamically in OnTick");
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                   |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   IndicatorRelease(fastMAHandle);
   IndicatorRelease(slowMAHandle);
   IndicatorRelease(atrHandle);
   IndicatorRelease(tmaHandle);
   IndicatorRelease(rsiHandle1);
   IndicatorRelease(rsiHandle2);
   IndicatorRelease(rsiHandle3);
   IndicatorRelease(rsiHandle4);
   IndicatorRelease(rsiHandle5);
   IndicatorRelease(rsiHandle6);
   IndicatorRelease(macdHandle);
   IndicatorRelease(bbHandle);
   IndicatorRelease(obvHandle);
   IndicatorRelease(vwapHandle);
   IndicatorRelease(stochasticHandle1);
   IndicatorRelease(stochasticHandle2);
   IndicatorRelease(stochasticHandle3);
   IndicatorRelease(mtf1FastEMAHandle);
   IndicatorRelease(mtf1SlowEMAHandle);
   IndicatorRelease(mtf2FastEMAHandle);
   IndicatorRelease(mtf2SlowEMAHandle);
   ObjectsDeleteAll(0, "Misape_");
}

//+------------------------------------------------------------------+
//| Expert tick function                                              |
//+------------------------------------------------------------------+
void OnTick()
{
   if(!tradingEnabled) return;

   // Update Markov matrix and NN on new bar
   if(TimeCurrent() >= lastBarTime + PeriodSeconds(_Period))
   {
      UpdateMarkovMatrix();
      barsSinceLastNNUpdate++;
      if(UseNeuralNetwork && barsSinceLastNNUpdate >= NNUpdateBars)
      {
         TrainNeuralNetwork();
         barsSinceLastNNUpdate = 0;
      }
      lastBarTime = TimeCurrent();
   }

   // Get indicator values
   ArrayResize(fastMA, 2);
   ArrayResize(slowMA, 2);
   ArrayResize(atr, 2);
   ArrayResize(tma, 2);
   ArrayResize(tmaUpper, 2);
   ArrayResize(tmaLower, 2);
   ArrayResize(rsi1, DivergenceBars);
   ArrayResize(rsi2, DivergenceBars);
   ArrayResize(rsi3, DivergenceBars);
   ArrayResize(rsi4, DivergenceBars);
   ArrayResize(rsi5, DivergenceBars);
   ArrayResize(rsi6, DivergenceBars);
   ArrayResize(macd, DivergenceBars);
   ArrayResize(bbUpper, DivergenceBars);
   ArrayResize(bbLower, DivergenceBars);
   ArrayResize(obv, DivergenceBars);
   ArrayResize(vwap, DivergenceBars);
   ArrayResize(stochastic1, DivergenceBars);
   ArrayResize(stochastic2, DivergenceBars);
   ArrayResize(stochastic3, DivergenceBars);
   ArrayResize(mtf1FastEMA, 2);
   ArrayResize(mtf1SlowEMA, 2);
   ArrayResize(mtf2FastEMA, 2);
   ArrayResize(mtf2SlowEMA, 2);

   // Check available bars
   int availableBars = Bars(_Symbol, _Period);
   if(availableBars < DivergenceBars + 1)
   {
      Print("Insufficient historical data: ", availableBars, " bars available, ", DivergenceBars + 1, " required");
      return;
   }

   // Copy standard indicator buffers
   if(CopyBuffer(fastMAHandle, 0, 0, 2, fastMA) < 2 ||
      CopyBuffer(slowMAHandle, 0, 0, 2, slowMA) < 2 ||
      CopyBuffer(atrHandle, 0, 0, 2, atr) < 2 ||
      CopyBuffer(rsiHandle1, 0, 0, DivergenceBars, rsi1) < DivergenceBars ||
      CopyBuffer(rsiHandle2, 0, 0, DivergenceBars, rsi2) < DivergenceBars ||
      CopyBuffer(rsiHandle3, 0, 0, DivergenceBars, rsi3) < DivergenceBars ||
      CopyBuffer(rsiHandle4, 0, 0, DivergenceBars, rsi4) < DivergenceBars ||
      CopyBuffer(rsiHandle5, 0, 0, DivergenceBars, rsi5) < DivergenceBars ||
      CopyBuffer(rsiHandle6, 0, 0, DivergenceBars, rsi6) < DivergenceBars ||
      CopyBuffer(macdHandle, 0, 0, DivergenceBars, macd) < DivergenceBars ||
      CopyBuffer(bbHandle, 1, 0, DivergenceBars, bbUpper) < DivergenceBars ||
      CopyBuffer(bbHandle, 2, 0, DivergenceBars, bbLower) < DivergenceBars ||
      CopyBuffer(obvHandle, 0, 0, DivergenceBars, obv) < DivergenceBars ||
      CopyBuffer(stochasticHandle1, 0, 0, DivergenceBars, stochastic1) < DivergenceBars ||
      CopyBuffer(stochasticHandle2, 0, 0, DivergenceBars, stochastic2) < DivergenceBars ||
      CopyBuffer(stochasticHandle3, 0, 0, DivergenceBars, stochastic3) < DivergenceBars ||
      CopyBuffer(mtf1FastEMAHandle, 0, 0, 2, mtf1FastEMA) < 2 ||
      CopyBuffer(mtf1SlowEMAHandle, 0, 0, 2, mtf1SlowEMA) < 2 ||
      CopyBuffer(mtf2FastEMAHandle, 0, 0, 2, mtf2FastEMA) < 2 ||
      CopyBuffer(mtf2SlowEMAHandle, 0, 0, 2, mtf2SlowEMA) < 2)
   {
      Print("Failed to copy indicator data");
      return;
   }

   // Calculate custom indicators (TMA and VWAP)
   if(!CalculateTMA(_Symbol, _Period, TMAPeriod))
   {
      Print("Failed to calculate TMA");
      return;
   }

   if(!CalculateVWAP(_Symbol, _Period, VWAPPeriod))
   {
      Print("Failed to calculate VWAP");
      return;
   }

   // Validate all arrays are properly sized before accessing
   if(!ValidateArraySizes())
   {
      Print("Array validation failed - skipping tick");
      return;
   }

   lastFastMA = fastMA[0];
   lastSlowMA = slowMA[0];
   prevFastMA = fastMA[1];
   prevSlowMA = slowMA[1];

   // Check MTF trend
   bool mtfBullish = mtf1FastEMA[0] > mtf1SlowEMA[0] && mtf2FastEMA[0] > mtf2SlowEMA[0];
   bool mtfBearish = mtf1FastEMA[0] < mtf1SlowEMA[0] && mtf2FastEMA[0] < mtf2SlowEMA[0];

   // Check TMA band expansion and momentum
   double tmaBandWidth = tmaUpper[0] - tmaLower[0];
   double prevTmaBandWidth = tmaUpper[1] - tmaLower[1];
   bool tmaExpanding = tmaBandWidth > prevTmaBandWidth;
   double price[];
   ArraySetAsSeries(price, true);
   ArrayResize(price, 3); // Increased to 3 for VWAP slope
   if(CopyClose(_Symbol, _Period, 0, 3, price) < 3)
   {
      Print("Failed to copy price data for momentum and VWAP");
      return;
   }
   double momentum = (price[0] - price[1]) / atr[0];
   bool bullishMomentum = momentum > 0.5;
   bool bearishMomentum = momentum < -0.5;

   // Check VWAP slope and deviation
   double vwapSlope = ArraySize(vwap) >= 3 ? vwap[1] - vwap[2] : 0.0; // Prevent array out of range
   bool vwapBullish = vwapSlope > 0 && price[0] > vwap[0];
   bool vwapBearish = vwapSlope < 0 && price[0] < vwap[0];

   // Get Markov state
   currentState = GetMarkovState();
   double bullishProb = markovMatrix[currentState][BULLISH];
   double bearishProb = markovMatrix[currentState][BEARISH];
   bool markovAllowsBuy = currentState == BULLISH || (currentState == NEUTRAL && bullishProb >= MarkovProbThreshold);
   bool markovAllowsSell = currentState == BEARISH || (currentState == NEUTRAL && bearishProb >= MarkovProbThreshold);

   // Dynamic signal threshold
   double longATR[];
   ArraySetAsSeries(longATR, true);
   ArrayResize(longATR, ATRPeriod * 2);
   if(CopyBuffer(atrHandle, 0, 0, ATRPeriod * 2, longATR) < ATRPeriod * 2)
   {
      Print("Failed to copy long ATR data");
      return;
   }
   double trendStrength = atr[0] / longATR[0];
   double signalThreshold = SignalThresholdBase * (1 - 0.5 * trendStrength);
   signalThreshold = MathMax(0.3, MathMin(signalThreshold, 0.7));

   // Check indicator signals with weighted voting
   double bullishScore = 0, bearishScore = 0;
   double rsiTemp[], stochasticTemp[];
   ArrayResize(rsiTemp, DivergenceBars);
   ArrayResize(stochasticTemp, DivergenceBars);

   // Process each RSI and Stochastic indicator
   for(int i = 0; i < 6; i++)
   {
      double currentRSI[], currentStochastic[];
      ArrayResize(currentRSI, DivergenceBars);
      ArrayResize(currentStochastic, DivergenceBars);
      switch(i)
      {
         case 0: ArrayCopy(currentRSI, rsi1, 0, 0, DivergenceBars); break;
         case 1: ArrayCopy(currentRSI, rsi2, 0, 0, DivergenceBars); break;
         case 2: ArrayCopy(currentRSI, rsi3, 0, 0, DivergenceBars); break;
         case 3: ArrayCopy(currentRSI, rsi4, 0, 0, DivergenceBars); break;
         case 4: ArrayCopy(currentRSI, rsi5, 0, 0, DivergenceBars); break;
         case 5: ArrayCopy(currentRSI, rsi6, 0, 0, DivergenceBars); break;
         default: continue;
      }
      switch(i % 3)
      {
         case 0: ArrayCopy(currentStochastic, stochastic1, 0, 0, DivergenceBars); break;
         case 1: ArrayCopy(currentStochastic, stochastic2, 0, 0, DivergenceBars); break;
         case 2: ArrayCopy(currentStochastic, stochastic3, 0, 0, DivergenceBars); break;
         default: continue;
      }
      if(DetectBullishSignal(currentRSI, "RSI") || DetectBullishDivergence(currentRSI, currentStochastic))
         bullishScore += WeightRSI;
      if(DetectBearishSignal(currentRSI, "RSI") || DetectBearishDivergence(currentRSI, currentStochastic))
         bearishScore += WeightRSI;
   }

   // Process MACD
   if(DetectBullishSignal(macd, "MACD") || DetectBullishDivergence(macd, stochastic1))
      bullishScore += WeightMACD;
   if(DetectBearishSignal(macd, "MACD") || DetectBearishDivergence(macd, stochastic1))
      bearishScore += WeightMACD;

   // Process Bollinger Bands
   if(DetectBullishBBSignal(bbUpper, bbLower, price) || DetectBullishBBDivergence(bbLower, stochastic1))
      bullishScore += WeightBB;
   if(DetectBearishBBSignal(bbUpper, bbLower, price) || DetectBearishBBDivergence(bbUpper, stochastic1))
      bearishScore += WeightBB;

   // Process OBV
   if(DetectBullishSignal(obv, "OBV") || DetectBullishDivergence(obv, stochastic1))
      bullishScore += WeightOBV;
   if(DetectBearishSignal(obv, "OBV") || DetectBearishDivergence(obv, stochastic1))
      bearishScore += WeightOBV;

   // Process VWAP
   if(DetectBullishSignal(vwap, "VWAP") || DetectBullishDivergence(vwap, stochastic1))
      bullishScore += WeightVWAP;
   if(DetectBearishSignal(vwap, "VWAP") || DetectBearishDivergence(vwap, stochastic1))
      bearishScore += WeightVWAP;

   // Process each Stochastic indicator
   for(int i = 0; i < 3; i++)
   {
      double currentStochastic[];
      ArrayResize(currentStochastic, DivergenceBars);
      switch(i)
      {
         case 0: ArrayCopy(currentStochastic, stochastic1, 0, 0, DivergenceBars); break;
         case 1: ArrayCopy(currentStochastic, stochastic2, 0, 0, DivergenceBars); break;
         case 2: ArrayCopy(currentStochastic, stochastic3, 0, 0, DivergenceBars); break;
         default: continue;
      }
      if(DetectBullishSignal(currentStochastic, "Stochastic") || DetectBullishDivergence(currentStochastic, currentStochastic))
         bullishScore += WeightStochastic;
      if(DetectBearishSignal(currentStochastic, "Stochastic") || DetectBearishDivergence(currentStochastic, currentStochastic))
         bearishScore += WeightStochastic;
   }

   // Neural network prediction
   double nnBuyProb = 0, nnSellProb = 0, nnHoldProb = 0;
   if(UseNeuralNetwork)
   {
      double inputs[INPUT_NEURONS];
      if(PrepareNNInputs(inputs))
      {
         ForwardPass(inputs);
         nnBuyProb = nnOutputs[0];
         nnSellProb = nnOutputs[1];
         nnHoldProb = nnOutputs[2];
      }
      else
      {
         Print("Skipping neural network prediction - inputs not ready");
      }
   }

   // Check TMA for APS
   bool priceAboveTMA = price[0] > tma[0];
   bool priceBelowTMA = price[0] < tma[0];

   // Update dashboard
   UpdateDashboard(bullishScore, bearishScore, currentState, bullishProb, bearishProb, nnBuyProb, nnSellProb, nnHoldProb);

   // Check for open positions
   bool hasBuy = false, hasSell = false;
   for(int i = PositionsTotal() - 1; i >= 0; i--)
   {
      ulong ticket = PositionGetTicket(i);
      if(PositionSelectByTicket(ticket) && PositionGetInteger(POSITION_MAGIC) == MagicNumber)
      {
         if(PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY)
            hasBuy = true;
         else if(PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_SELL)
            hasSell = true;
         UpdateTrailingStop(ticket);
      }
   }

   // Get current price
   MqlTick tick;
   if(!SymbolInfoTick(_Symbol, tick))
   {
      Print("Failed to get tick data");
      return;
   }
   double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
   double sl = StopLoss * point;
   double tp = TakeProfit * point;

   // Dynamic RSI thresholds
   double rsiBuyLevel = 30 + atr[0] * 1000;
   double rsiSellLevel = 70 - atr[0] * 1000;
   rsiBuyLevel = MathMax(20, MathMin(rsiBuyLevel, 40));
   rsiSellLevel = MathMin(80, MathMax(rsiSellLevel, 60));

   // Analyze individual trading conditions for debugging
   bool maCrossoverBuy = lastFastMA > lastSlowMA && prevFastMA <= prevSlowMA;
   bool maCrossoverSell = lastFastMA < lastSlowMA && prevFastMA >= prevSlowMA;
   bool scoreConditionBuy = bullishScore >= signalThreshold;
   bool scoreConditionSell = bearishScore >= signalThreshold;
   bool rsiConditionBuy = DetectBullishSignal(rsi1, "RSI") || DetectBullishDivergence(rsi1, stochastic1);
   bool rsiConditionSell = DetectBearishSignal(rsi1, "RSI") || DetectBearishDivergence(rsi1, stochastic1);

   // Choose trading strategy based on SimplifiedTrading parameter
   bool allowBuy, allowSell;

   if(SimplifiedTrading)
   {
      // Simplified conditions - easier to trigger trades for testing
      allowBuy = maCrossoverBuy && scoreConditionBuy && !hasBuy;
      allowSell = maCrossoverSell && scoreConditionSell && !hasSell;
   }
   else
   {
      // Original complex conditions - all requirements must be met
      allowBuy = maCrossoverBuy && scoreConditionBuy && mtfBullish && priceAboveTMA &&
                 tmaExpanding && vwapBullish && bullishMomentum && markovAllowsBuy && !hasBuy && rsiConditionBuy;
      allowSell = maCrossoverSell && scoreConditionSell && mtfBearish && priceBelowTMA &&
                  tmaExpanding && vwapBearish && bearishMomentum && markovAllowsSell && !hasSell && rsiConditionSell;
   }

   // Add neural network conditions if enabled
   if(UseNeuralNetwork)
   {
      if(SimplifiedTrading)
      {
         // Simplified NN conditions - just check if buy/sell is stronger than hold
         allowBuy = allowBuy && nnBuyProb > nnHoldProb;
         allowSell = allowSell && nnSellProb > nnHoldProb;
      }
      else
      {
         // Original strict NN conditions
         allowBuy = allowBuy && nnBuyProb >= MarkovProbThreshold && nnBuyProb > nnSellProb && nnBuyProb > nnHoldProb;
         allowSell = allowSell && nnSellProb >= MarkovProbThreshold && nnSellProb > nnBuyProb && nnSellProb > nnHoldProb;
      }
   }

   // Debug output every 100 ticks to avoid spam
   static int debugCounter = 0;
   debugCounter++;
   if(debugCounter >= 100)
   {
      debugCounter = 0;
      Print("=== TRADING CONDITIONS DEBUG ===");
      Print("MA Crossover Buy: ", maCrossoverBuy, " | MA Crossover Sell: ", maCrossoverSell);
      Print("Score Buy: ", scoreConditionBuy, " (", DoubleToString(bullishScore, 2), "/", DoubleToString(signalThreshold, 2), ")");
      Print("Score Sell: ", scoreConditionSell, " (", DoubleToString(bearishScore, 2), "/", DoubleToString(signalThreshold, 2), ")");
      Print("Markov Buy: ", markovAllowsBuy, " | Markov Sell: ", markovAllowsSell);
      Print("RSI Buy: ", rsiConditionBuy, " | RSI Sell: ", rsiConditionSell);
      Print("Has Buy: ", hasBuy, " | Has Sell: ", hasSell);
      if(UseNeuralNetwork)
      {
         Print("NN Buy: ", DoubleToString(nnBuyProb, 3), " | NN Sell: ", DoubleToString(nnSellProb, 3), " | NN Hold: ", DoubleToString(nnHoldProb, 3));
      }
      Print("FINAL: Allow Buy: ", allowBuy, " | Allow Sell: ", allowSell);
      Print("================================");
   }

   // Execute trades with retry logic
   MqlTradeRequest request;
   MqlTradeResult result;
   request.symbol = _Symbol;
   request.volume = LotSize;
   request.magic = MagicNumber;

   // Buy signal
   if(allowBuy)
   {
      Print("*** BUY SIGNAL TRIGGERED ***");
      Print("Current Price: ", DoubleToString(tick.ask, _Digits));
      Print("Fast MA: ", DoubleToString(lastFastMA, _Digits), " | Slow MA: ", DoubleToString(lastSlowMA, _Digits));
      Print("Bullish Score: ", DoubleToString(bullishScore, 2), " | Threshold: ", DoubleToString(signalThreshold, 2));
      if(hasSell)
         CloseAllPositions(POSITION_TYPE_SELL);
      request.action = TRADE_ACTION_DEAL;
      request.type = ORDER_TYPE_BUY;
      request.price = tick.ask;
      request.sl = tick.ask - sl;
      request.tp = tick.ask + tp;
      for(int retry = 0; retry < 3; retry++)
      {
         if(OrderSend(request, result))
         {
            if(result.retcode == TRADE_RETCODE_DONE)
            {
               totalTrades++;
               break;
            }
            else if(result.retcode == TRADE_RETCODE_REQUOTE || result.retcode == TRADE_RETCODE_PRICE_OFF)
            {
               SymbolInfoTick(_Symbol, tick);
               request.price = tick.ask;
               request.sl = tick.ask - sl;
               request.tp = tick.ask + tp;
               continue;
            }
            else
            {
               Print("Buy order failed, retcode=", result.retcode);
               break;
            }
         }
         Sleep(100);
      }
   }
   // Sell signal
   else if(allowSell)
   {
      Print("*** SELL SIGNAL TRIGGERED ***");
      Print("Current Price: ", DoubleToString(tick.bid, _Digits));
      Print("Fast MA: ", DoubleToString(lastFastMA, _Digits), " | Slow MA: ", DoubleToString(lastSlowMA, _Digits));
      Print("Bearish Score: ", DoubleToString(bearishScore, 2), " | Threshold: ", DoubleToString(signalThreshold, 2));
      if(hasBuy)
         CloseAllPositions(POSITION_TYPE_BUY);
      request.action = TRADE_ACTION_DEAL;
      request.type = ORDER_TYPE_SELL;
      request.price = tick.bid;
      request.sl = tick.bid + sl;
      request.tp = tick.bid - tp;
      for(int retry = 0; retry < 3; retry++)
      {
         if(OrderSend(request, result))
         {
            if(result.retcode == TRADE_RETCODE_DONE)
            {
               totalTrades++;
               break;
            }
            else if(result.retcode == TRADE_RETCODE_REQUOTE || result.retcode == TRADE_RETCODE_PRICE_OFF)
            {
               SymbolInfoTick(_Symbol, tick);
               request.price = tick.bid;
               request.sl = tick.bid + sl;
               request.tp = tick.bid - tp;
               continue;
            }
            else
            {
               Print("Sell order failed, retcode=", result.retcode);
               break;
            }
         }
         Sleep(100);
      }
   }
}

//+------------------------------------------------------------------+
//| Trade event handler                                               |
//+------------------------------------------------------------------+
void OnTrade()
{
   for(int i = HistoryDealsTotal() - 1; i >= 0; i--)
   {
      ulong dealTicket = HistoryDealGetTicket(i);
      if(HistoryDealSelect(dealTicket) && HistoryDealGetInteger(dealTicket, DEAL_MAGIC) == MagicNumber)
      {
         double profit = HistoryDealGetDouble(dealTicket, DEAL_PROFIT);
         totalProfit += profit;
         if(profit > 0)
            winningTrades++;
      }
   }
}

//+------------------------------------------------------------------+
//| Chart event handler                                               |
//+------------------------------------------------------------------+
void OnChartEvent(const int id, const long &lparam, const double &dparam, const string &sparam)
{
   if(id == CHARTEVENT_OBJECT_CLICK && sparam == "Misape_ToggleTrading")
   {
      tradingEnabled = !tradingEnabled;
      UpdateDashboard(0, 0, currentState, 0, 0, 0, 0, 0);
   }
}

//+------------------------------------------------------------------+
//| Create dashboard                                                  |
//+------------------------------------------------------------------+
void CreateDashboard()
{
   ObjectsDeleteAll(0, "Misape_");

   int y = 20;
   CreateLabel("Misape_Title", "Misape Smart Bot", 10, y, clrAqua, 14, "Arial", ANCHOR_LEFT_UPPER);
   y += 30;
   CreateLabel("Misape_Symbol", "Symbol: " + _Symbol, 10, y, clrWhite, 10, "Arial", ANCHOR_LEFT_UPPER);
   y += 20;
   CreateLabel("Misape_Price", "Price: ", 10, y, clrWhite, 10, "Arial", ANCHOR_LEFT_UPPER);
   y += 20;
   CreateLabel("Misape_State", "Market State: ", 10, y, clrWhite, 10, "Arial", ANCHOR_LEFT_UPPER);
   y += 20;
   CreateLabel("Misape_BullishProb", "Bullish Prob: ", 10, y, clrWhite, 10, "Arial", ANCHOR_LEFT_UPPER);
   y += 20;
   CreateLabel("Misape_BearishProb", "Bearish Prob: ", 10, y, clrWhite, 10, "Arial", ANCHOR_LEFT_UPPER);
   y += 20;
   CreateLabel("Misape_BullishScore", "Bullish Score: ", 10, y, clrWhite, 10, "Arial", ANCHOR_LEFT_UPPER);
   y += 20;
   CreateLabel("Misape_BearishScore", "Bearish Score: ", 10, y, clrWhite, 10, "Arial", ANCHOR_LEFT_UPPER);
   y += 20;
   CreateLabel("Misape_NNBuy", "NN Buy Prob: ", 10, y, clrWhite, 10, "Arial", ANCHOR_LEFT_UPPER);
   y += 20;
   CreateLabel("Misape_NNSell", "NN Sell Prob: ", 10, y, clrWhite, 10, "Arial", ANCHOR_LEFT_UPPER);
   y += 20;
   CreateLabel("Misape_NNHold", "NN Hold Prob: ", 10, y, clrWhite, 10, "Arial", ANCHOR_LEFT_UPPER);
   y += 20;
   CreateLabel("Misape_TMACenter", "TMA Center: ", 10, y, clrWhite, 10, "Arial", ANCHOR_LEFT_UPPER);
   y += 20;
   CreateLabel("Misape_TMAUpper", "TMA Upper: ", 10, y, clrWhite, 10, "Arial", ANCHOR_LEFT_UPPER);
   y += 20;
   CreateLabel("Misape_TMALower", "TMA Lower: ", 10, y, clrWhite, 10, "Arial", ANCHOR_LEFT_UPPER);
   y += 20;
   CreateLabel("Misape_VWAP", "VWAP: ", 10, y, clrWhite, 10, "Arial", ANCHOR_LEFT_UPPER);
   y += 20;
   CreateLabel("Misape_Trades", "Total Trades: ", 10, y, clrWhite, 10, "Arial", ANCHOR_LEFT_UPPER);
   y += 20;
   CreateLabel("Misape_WinRate", "Win Rate: ", 10, y, clrWhite, 10, "Arial", ANCHOR_LEFT_UPPER);
   y += 20;
   CreateLabel("Misape_Profit", "Total Profit: ", 10, y, clrWhite, 10, "Arial", ANCHOR_LEFT_UPPER);
   y += 20;
   CreateLabel("Misape_Trading", "Trading: ", 10, y, clrWhite, 10, "Arial", ANCHOR_LEFT_UPPER);
   y += 30;
   CreateButton("Misape_ToggleTrading", tradingEnabled ? "Pause Trading" : "Resume Trading", 10, y, 120, 30, clrAqua, clrBlack);
}

//+------------------------------------------------------------------+
//| Update dashboard                                                  |
//+------------------------------------------------------------------+
void UpdateDashboard(double bullishScore, double bearishScore, MARKET_STATE state, double bullishProb, double bearishProb,
                     double nnBuyProb, double nnSellProb, double nnHoldProb)
{
   string stateStr = state == BULLISH ? "Bullish" : state == BEARISH ? "Bearish" : "Neutral";
   double winRate = totalTrades > 0 ? (double)winningTrades / totalTrades * 100 : 0;
   double currentPrice = SymbolInfoDouble(_Symbol, SYMBOL_BID);

   ObjectSetString(0, "Misape_Symbol", OBJPROP_TEXT, "Symbol: " + _Symbol);
   ObjectSetString(0, "Misape_Price", OBJPROP_TEXT, "Price: " + DoubleToString(currentPrice, _Digits));
   ObjectSetString(0, "Misape_State", OBJPROP_TEXT, "Market State: " + stateStr);
   ObjectSetString(0, "Misape_BullishProb", OBJPROP_TEXT, "Bullish Prob: " + DoubleToString(bullishProb, 2));
   ObjectSetString(0, "Misape_BearishProb", OBJPROP_TEXT, "Bearish Prob: " + DoubleToString(bearishProb, 2));
   ObjectSetString(0, "Misape_BullishScore", OBJPROP_TEXT, "Bullish Score: " + DoubleToString(bullishScore, 2));
   ObjectSetString(0, "Misape_BearishScore", OBJPROP_TEXT, "Bearish Score: " + DoubleToString(bearishScore, 2));
   ObjectSetString(0, "Misape_NNBuy", OBJPROP_TEXT, "NN Buy Prob: " + DoubleToString(nnBuyProb, 2));
   ObjectSetString(0, "Misape_NNSell", OBJPROP_TEXT, "NN Sell Prob: " + DoubleToString(nnSellProb, 2));
   ObjectSetString(0, "Misape_NNHold", OBJPROP_TEXT, "NN Hold Prob: " + DoubleToString(nnHoldProb, 2));
   // Safe array access for dashboard updates
   ObjectSetString(0, "Misape_TMACenter", OBJPROP_TEXT, "TMA Center: " +
      (ArraySize(tma) > 0 ? DoubleToString(tma[0], _Digits) : "N/A"));
   ObjectSetString(0, "Misape_TMAUpper", OBJPROP_TEXT, "TMA Upper: " +
      (ArraySize(tmaUpper) > 0 ? DoubleToString(tmaUpper[0], _Digits) : "N/A"));
   ObjectSetString(0, "Misape_TMALower", OBJPROP_TEXT, "TMA Lower: " +
      (ArraySize(tmaLower) > 0 ? DoubleToString(tmaLower[0], _Digits) : "N/A"));
   ObjectSetString(0, "Misape_VWAP", OBJPROP_TEXT, "VWAP: " +
      (ArraySize(vwap) > 0 ? DoubleToString(vwap[0], _Digits) : "N/A"));
   ObjectSetString(0, "Misape_Trades", OBJPROP_TEXT, "Total Trades: " + IntegerToString(totalTrades));
   ObjectSetString(0, "Misape_WinRate", OBJPROP_TEXT, "Win Rate: " + DoubleToString(winRate, 2) + "%");
   ObjectSetString(0, "Misape_Profit", OBJPROP_TEXT, "Total Profit: " + DoubleToString(totalProfit, 2));
   ObjectSetString(0, "Misape_Trading", OBJPROP_TEXT, "Trading: " + (tradingEnabled ? "Enabled" : "Disabled"));
   ObjectSetString(0, "Misape_ToggleTrading", OBJPROP_TEXT, tradingEnabled ? "Pause Trading" : "Resume Trading");
   ObjectSetInteger(0, "Misape_ToggleTrading", OBJPROP_BGCOLOR, tradingEnabled ? clrAqua : clrRed);
   ChartRedraw();
}

//+------------------------------------------------------------------+
//| Create label                                                     |
//+------------------------------------------------------------------+
void CreateLabel(string name, string text, int x, int y, color clr, int size, string font, ENUM_ANCHOR_POINT anchor)
{
   if(ObjectFind(0, name) < 0)
   {
      ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0);
      ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
      ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
      ObjectSetString(0, name, OBJPROP_TEXT, text);
      ObjectSetInteger(0, name, OBJPROP_COLOR, clr);
      ObjectSetInteger(0, name, OBJPROP_FONTSIZE, size);
      ObjectSetString(0, name, OBJPROP_FONT, font);
      ObjectSetInteger(0, name, OBJPROP_ANCHOR, anchor);
   }
}

//+------------------------------------------------------------------+
//| Create button                                                    |
//+------------------------------------------------------------------+
void CreateButton(string name, string text, int x, int y, int width, int height, color bgColor, color textColor)
{
   if(ObjectFind(0, name) < 0)
   {
      ObjectCreate(0, name, OBJ_BUTTON, 0, 0, 0);
      ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
      ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
      ObjectSetInteger(0, name, OBJPROP_XSIZE, width);
      ObjectSetInteger(0, name, OBJPROP_YSIZE, height);
      ObjectSetString(0, name, OBJPROP_TEXT, text);
      ObjectSetInteger(0, name, OBJPROP_BGCOLOR, bgColor);
      ObjectSetInteger(0, name, OBJPROP_COLOR, textColor);
      ObjectSetInteger(0, name, OBJPROP_FONTSIZE, 10);
      ObjectSetString(0, name, OBJPROP_FONT, "Arial");
   }
}

//+------------------------------------------------------------------+
//| Initialize neural network                                         |
//+------------------------------------------------------------------+
void InitializeNeuralNetwork()
{
   MathSrand((uint)TimeCurrent());
   for(int i = 0; i < INPUT_NEURONS; i++)
      for(int j = 0; j < HIDDEN_NEURONS; j++)
         nnWeights1[i][j] = (MathRand() / 32767.0 - 0.5) * 0.1;
   for(int i = 0; i < HIDDEN_NEURONS; i++)
      for(int j = 0; j < HIDDEN_NEURONS; j++)
         nnWeights2[i][j] = (MathRand() / 32767.0 - 0.5) * 0.1;
   for(int i = 0; i < HIDDEN_NEURONS; i++)
      for(int j = 0; j < OUTPUT_NEURONS; j++)
         nnWeights3[i][j] = (MathRand() / 32767.0 - 0.5) * 0.1;
   ArrayInitialize(nnBias1, 0.0);
   ArrayInitialize(nnBias2, 0.0);
   ArrayInitialize(nnBias3, 0.0);
}

//+------------------------------------------------------------------+
//| Train neural network                                              |
//+------------------------------------------------------------------+
bool TrainNeuralNetwork()
{
   double price[];
   ArraySetAsSeries(price, true);
   ArrayResize(price, NNTrainingBars + 1);
   if(CopyClose(_Symbol, _Period, 0, NNTrainingBars + 1, price) < NNTrainingBars + 1)
   {
      Print("Insufficient historical data for neural network training: ", Bars(_Symbol, _Period), " bars available, ", NNTrainingBars + 1, " required");
      return false;
   }

   for(int i = 0; i < NNTrainingBars; i++)
   {
      double inputs[INPUT_NEURONS];
      double targets[OUTPUT_NEURONS] = {0.0, 0.0, 0.0};
      if(!PrepareNNInputs(inputs))
      {
         Print("Skipping neural network training iteration ", i, " - inputs not ready");
         continue;
      }
      if(price[i] > price[i + 1])
         targets[0] = 1.0; // Buy
      else if(price[i] < price[i + 1])
         targets[1] = 1.0; // Sell
      else
         targets[2] = 1.0; // Hold
      for(int epoch = 0; epoch < NNTrainingEpochs; epoch++)
      {
         ForwardPass(inputs);
         Backpropagate(inputs, targets);
      }
   }
   return true;
}

//+------------------------------------------------------------------+
//| Prepare neural network inputs - Safe version with validation      |
//+------------------------------------------------------------------+
bool PrepareNNInputs(double &inputs[])
{
   ArrayResize(inputs, INPUT_NEURONS);

   // Validate all required arrays are properly sized
   if(ArraySize(rsi1) < 1 || ArraySize(rsi2) < 1 || ArraySize(rsi3) < 1 ||
      ArraySize(macd) < 1 || ArraySize(bbUpper) < 1 || ArraySize(bbLower) < 1 ||
      ArraySize(obv) < 1 || ArraySize(vwap) < 1 || ArraySize(stochastic1) < 1 ||
      ArraySize(tma) < 1 || ArraySize(tmaUpper) < 1 || ArraySize(tmaLower) < 1 ||
      ArraySize(atr) < 1)
   {
      Print("Neural network inputs: Required indicator arrays not properly initialized");
      return false;
   }

   double price[];
   ArraySetAsSeries(price, true);
   ArrayResize(price, DivergenceBars);
   if(CopyClose(_Symbol, _Period, 0, DivergenceBars, price) < DivergenceBars)
   {
      Print("Failed to copy price data for neural network inputs");
      return false;
   }

   // Safely populate inputs with validation
   inputs[0] = Normalize(lastFastMA - lastSlowMA, -1000, 1000);
   inputs[1] = Normalize(rsi1[0], 0, 100);
   inputs[2] = Normalize(rsi2[0], 0, 100);
   inputs[3] = Normalize(rsi3[0], 0, 100);
   inputs[4] = Normalize(macd[0], -100, 100);
   inputs[5] = Normalize(price[0] - bbLower[0], 0, 1000);
   inputs[6] = Normalize(bbUpper[0] - price[0], 0, 1000);
   inputs[7] = Normalize(obv[0], -1000000, 1000000);
   inputs[8] = Normalize(vwap[0], price[0] - 1000, price[0] + 1000);
   inputs[9] = Normalize(stochastic1[0], 0, 100);
   inputs[10] = Normalize(tmaUpper[0] - tmaLower[0], 0, 1000);
   inputs[11] = Normalize(atr[0], 0, 0.01);

   return true;
}

//+------------------------------------------------------------------+
//| Neural network forward pass                                       |
//+------------------------------------------------------------------+
void ForwardPass(double &inputs[])
{
   double hidden1[HIDDEN_NEURONS], hidden2[HIDDEN_NEURONS];
   ArrayInitialize(hidden1, 0.0);
   ArrayInitialize(hidden2, 0.0);
   ArrayInitialize(nnOutputs, 0.0);

   for(int i = 0; i < HIDDEN_NEURONS; i++)
   {
      for(int j = 0; j < INPUT_NEURONS; j++)
         hidden1[i] += inputs[j] * nnWeights1[j][i];
      hidden1[i] += nnBias1[i];
      hidden1[i] = Sigmoid(hidden1[i]);
   }
   for(int i = 0; i < HIDDEN_NEURONS; i++)
   {
      for(int j = 0; j < HIDDEN_NEURONS; j++)
         hidden2[i] += hidden1[j] * nnWeights2[j][i];
      hidden2[i] += nnBias2[i];
      hidden2[i] = Sigmoid(hidden2[i]);
   }
   for(int i = 0; i < OUTPUT_NEURONS; i++)
   {
      for(int j = 0; j < HIDDEN_NEURONS; j++)
         nnOutputs[i] += hidden2[j] * nnWeights3[j][i];
      nnOutputs[i] += nnBias3[i];
      nnOutputs[i] = Sigmoid(nnOutputs[i]);
   }
   double sum = nnOutputs[0] + nnOutputs[1] + nnOutputs[2];
   for(int i = 0; i < OUTPUT_NEURONS; i++)
      nnOutputs[i] /= sum;
}

//+------------------------------------------------------------------+
//| Neural network backpropagation                                    |
//+------------------------------------------------------------------+
void Backpropagate(double &inputs[], double &targets[])
{
   double hidden1[HIDDEN_NEURONS], hidden2[HIDDEN_NEURONS];
   ArrayInitialize(hidden1, 0.0);
   ArrayInitialize(hidden2, 0.0);

   for(int i = 0; i < HIDDEN_NEURONS; i++)
   {
      for(int j = 0; j < INPUT_NEURONS; j++)
         hidden1[i] += inputs[j] * nnWeights1[j][i];
      hidden1[i] += nnBias1[i];
      hidden1[i] = Sigmoid(hidden1[i]);
   }
   for(int i = 0; i < HIDDEN_NEURONS; i++)
   {
      for(int j = 0; j < HIDDEN_NEURONS; j++)
         hidden2[i] += hidden1[j] * nnWeights2[j][i];
      hidden2[i] += nnBias2[i];
      hidden2[i] = Sigmoid(hidden2[i]);
   }

   double deltaOutput[OUTPUT_NEURONS], deltaHidden2[HIDDEN_NEURONS], deltaHidden1[HIDDEN_NEURONS];
   for(int i = 0; i < OUTPUT_NEURONS; i++)
   {
      deltaOutput[i] = (targets[i] - nnOutputs[i]) * nnOutputs[i] * (1 - nnOutputs[i]);
      for(int j = 0; j < HIDDEN_NEURONS; j++)
         nnWeights3[j][i] += NNLearningRate * deltaOutput[i] * hidden2[j];
      nnBias3[i] += NNLearningRate * deltaOutput[i];
   }
   for(int i = 0; i < HIDDEN_NEURONS; i++)
   {
      deltaHidden2[i] = 0;
      for(int j = 0; j < OUTPUT_NEURONS; j++)
         deltaHidden2[i] += deltaOutput[j] * nnWeights3[i][j];
      deltaHidden2[i] *= hidden2[i] * (1 - hidden2[i]);
      for(int j = 0; j < HIDDEN_NEURONS; j++)
         nnWeights2[j][i] += NNLearningRate * deltaHidden2[i] * hidden1[j];
      nnBias2[i] += NNLearningRate * deltaHidden2[i];
   }
   for(int i = 0; i < HIDDEN_NEURONS; i++)
   {
      deltaHidden1[i] = 0;
      for(int j = 0; j < HIDDEN_NEURONS; j++)
         deltaHidden1[i] += deltaHidden2[j] * nnWeights2[i][j];
      deltaHidden1[i] *= hidden1[i] * (1 - hidden1[i]);
      for(int j = 0; j < INPUT_NEURONS; j++)
         nnWeights1[j][i] += NNLearningRate * deltaHidden1[i] * inputs[j];
      nnBias1[i] += NNLearningRate * deltaHidden1[i];
   }
}

//+------------------------------------------------------------------+
//| Sigmoid activation function                                       |
//+------------------------------------------------------------------+
double Sigmoid(double x)
{
   return 1.0 / (1.0 + MathExp(-x));
}

//+------------------------------------------------------------------+
//| Normalize function                                                |
//+------------------------------------------------------------------+
double Normalize(double value, double min, double max)
{
   if(max == min)
   {
      Print("Warning: Normalization range is zero");
      return 0.5;
   }
   return (value - min) / (max - min);
}

//+------------------------------------------------------------------+
//| Calculate Triangular Moving Average (TMA) - Safe version for OnTick |
//+------------------------------------------------------------------+
bool CalculateTMA(string symbol, ENUM_TIMEFRAMES timeframe, int period)
{
   // Check if we have enough bars
   if(Bars(symbol, timeframe) < period + ATRPeriod)
   {
      Print("Insufficient bars for TMA calculation: ", Bars(symbol, timeframe), " available, ", period + ATRPeriod, " required");
      return false;
   }

   int smaHandle = iMA(symbol, timeframe, period, 0, MODE_SMA, PRICE_CLOSE);
   if(smaHandle == INVALID_HANDLE)
   {
      Print("Failed to create SMA handle for TMA calculation");
      return false;
   }

   double sma[];
   ArraySetAsSeries(sma, true);
   ArrayResize(sma, period);
   if(CopyBuffer(smaHandle, 0, 0, period, sma) < period)
   {
      Print("Failed to copy SMA buffer for TMA calculation");
      IndicatorRelease(smaHandle);
      return false;
   }

   double tmaValue = 0;
   for(int i = 0; i < period; i++)
      tmaValue += sma[i] * (period - MathAbs(i - period / 2));
   tmaValue /= (period * (period + 1) / 2);

   double atrValue[];
   ArraySetAsSeries(atrValue, true);
   ArrayResize(atrValue, 1);
   if(CopyBuffer(atrHandle, 0, 0, 1, atrValue) < 1)
   {
      Print("Failed to copy ATR buffer for TMA calculation");
      IndicatorRelease(smaHandle);
      return false;
   }

   // Ensure TMA arrays are properly sized
   ArraySetAsSeries(tma, true);
   ArraySetAsSeries(tmaUpper, true);
   ArraySetAsSeries(tmaLower, true);
   ArrayResize(tma, 1);
   ArrayResize(tmaUpper, 1);
   ArrayResize(tmaLower, 1);

   tma[0] = tmaValue;
   tmaUpper[0] = tmaValue + atrValue[0] * 2;
   tmaLower[0] = tmaValue - atrValue[0] * 2;
   IndicatorRelease(smaHandle);
   return true;
}

//+------------------------------------------------------------------+
//| Calculate Volume Weighted Average Price (VWAP) - Safe version for OnTick |
//+------------------------------------------------------------------+
bool CalculateVWAP(string symbol, ENUM_TIMEFRAMES timeframe, int period)
{
   // Check if we have enough bars
   if(Bars(symbol, timeframe) < period)
   {
      Print("Insufficient bars for VWAP calculation: ", Bars(symbol, timeframe), " available, ", period, " required");
      return false;
   }

   double price[];
   long volume[];
   ArraySetAsSeries(price, true);
   ArraySetAsSeries(volume, true);
   ArrayResize(price, period);
   ArrayResize(volume, period);

   if(CopyClose(symbol, timeframe, 0, period, price) < period)
   {
      Print("Failed to copy price data for VWAP calculation");
      return false;
   }

   if(CopyTickVolume(symbol, timeframe, 0, period, volume) < period)
   {
      Print("Failed to copy volume data for VWAP calculation");
      return false;
   }

   // Ensure vwap array is properly sized
   ArraySetAsSeries(vwap, true);
   ArrayResize(vwap, 1);

   double sumPV = 0, sumV = 0;
   for(int i = 0; i < period; i++)
   {
      sumPV += price[i] * (double)volume[i];
      sumV += (double)volume[i];
   }
   vwap[0] = sumV != 0 ? sumPV / sumV : 0;
   return true;
}

//+------------------------------------------------------------------+
//| Initialize all arrays to prevent array out of range errors       |
//+------------------------------------------------------------------+
void InitializeArrays()
{
   // Set all arrays as time series and initialize with minimum size
   ArraySetAsSeries(fastMA, true);
   ArraySetAsSeries(slowMA, true);
   ArraySetAsSeries(rsi1, true);
   ArraySetAsSeries(rsi2, true);
   ArraySetAsSeries(rsi3, true);
   ArraySetAsSeries(rsi4, true);
   ArraySetAsSeries(rsi5, true);
   ArraySetAsSeries(rsi6, true);
   ArraySetAsSeries(macd, true);
   ArraySetAsSeries(bbUpper, true);
   ArraySetAsSeries(bbLower, true);
   ArraySetAsSeries(obv, true);
   ArraySetAsSeries(vwap, true);
   ArraySetAsSeries(stochastic1, true);
   ArraySetAsSeries(stochastic2, true);
   ArraySetAsSeries(stochastic3, true);
   ArraySetAsSeries(tma, true);
   ArraySetAsSeries(tmaUpper, true);
   ArraySetAsSeries(tmaLower, true);
   ArraySetAsSeries(atr, true);
   ArraySetAsSeries(mtf1FastEMA, true);
   ArraySetAsSeries(mtf1SlowEMA, true);
   ArraySetAsSeries(mtf2FastEMA, true);
   ArraySetAsSeries(mtf2SlowEMA, true);

   // Initialize arrays with minimum required size (1 element with default value)
   ArrayResize(fastMA, 1);
   ArrayResize(slowMA, 1);
   ArrayResize(rsi1, 1);
   ArrayResize(rsi2, 1);
   ArrayResize(rsi3, 1);
   ArrayResize(rsi4, 1);
   ArrayResize(rsi5, 1);
   ArrayResize(rsi6, 1);
   ArrayResize(macd, 1);
   ArrayResize(bbUpper, 1);
   ArrayResize(bbLower, 1);
   ArrayResize(obv, 1);
   ArrayResize(vwap, 1);
   ArrayResize(stochastic1, 1);
   ArrayResize(stochastic2, 1);
   ArrayResize(stochastic3, 1);
   ArrayResize(tma, 1);
   ArrayResize(tmaUpper, 1);
   ArrayResize(tmaLower, 1);
   ArrayResize(atr, 1);
   ArrayResize(mtf1FastEMA, 1);
   ArrayResize(mtf1SlowEMA, 1);
   ArrayResize(mtf2FastEMA, 1);
   ArrayResize(mtf2SlowEMA, 1);

   // Initialize with safe default values
   fastMA[0] = 0.0;
   slowMA[0] = 0.0;
   rsi1[0] = 50.0;
   rsi2[0] = 50.0;
   rsi3[0] = 50.0;
   rsi4[0] = 50.0;
   rsi5[0] = 50.0;
   rsi6[0] = 50.0;
   macd[0] = 0.0;
   bbUpper[0] = 0.0;
   bbLower[0] = 0.0;
   obv[0] = 0.0;
   vwap[0] = 0.0;
   stochastic1[0] = 50.0;
   stochastic2[0] = 50.0;
   stochastic3[0] = 50.0;
   tma[0] = 0.0;
   tmaUpper[0] = 0.0;
   tmaLower[0] = 0.0;
   atr[0] = 0.0001; // Small non-zero value to prevent division by zero
   mtf1FastEMA[0] = 0.0;
   mtf1SlowEMA[0] = 0.0;
   mtf2FastEMA[0] = 0.0;
   mtf2SlowEMA[0] = 0.0;

   Print("All arrays initialized with safe default values");
}

//+------------------------------------------------------------------+
//| Validate all arrays are properly sized                           |
//+------------------------------------------------------------------+
bool ValidateArraySizes()
{
   // Check all critical arrays have minimum required size
   if(ArraySize(fastMA) < 2)
   {
      Print("FastMA array not properly sized: ", ArraySize(fastMA));
      return false;
   }
   if(ArraySize(slowMA) < 2)
   {
      Print("SlowMA array not properly sized: ", ArraySize(slowMA));
      return false;
   }
   if(ArraySize(atr) < 2)
   {
      Print("ATR array not properly sized: ", ArraySize(atr));
      return false;
   }
   if(ArraySize(rsi1) < 1 || ArraySize(rsi2) < 1 || ArraySize(rsi3) < 1 ||
      ArraySize(rsi4) < 1 || ArraySize(rsi5) < 1 || ArraySize(rsi6) < 1)
   {
      Print("RSI arrays not properly sized");
      return false;
   }
   if(ArraySize(macd) < 1)
   {
      Print("MACD array not properly sized: ", ArraySize(macd));
      return false;
   }
   if(ArraySize(bbUpper) < 1 || ArraySize(bbLower) < 1)
   {
      Print("Bollinger Bands arrays not properly sized");
      return false;
   }
   if(ArraySize(obv) < 1)
   {
      Print("OBV array not properly sized: ", ArraySize(obv));
      return false;
   }
   if(ArraySize(vwap) < 1)
   {
      Print("VWAP array not properly sized: ", ArraySize(vwap));
      return false;
   }
   if(ArraySize(stochastic1) < 1 || ArraySize(stochastic2) < 1 || ArraySize(stochastic3) < 1)
   {
      Print("Stochastic arrays not properly sized");
      return false;
   }
   if(ArraySize(tma) < 1 || ArraySize(tmaUpper) < 2 || ArraySize(tmaLower) < 2)
   {
      Print("TMA arrays not properly sized");
      return false;
   }
   if(ArraySize(mtf1FastEMA) < 1 || ArraySize(mtf1SlowEMA) < 1 ||
      ArraySize(mtf2FastEMA) < 1 || ArraySize(mtf2SlowEMA) < 1)
   {
      Print("Multi-timeframe EMA arrays not properly sized");
      return false;
   }

   return true;
}

//+------------------------------------------------------------------+
//| Update Markov matrix                                              |
//+------------------------------------------------------------------+
void UpdateMarkovMatrix()
{
   double price[];
   ArraySetAsSeries(price, true);
   ArrayResize(price, MarkovBars + 1);
   if(CopyClose(_Symbol, _Period, 0, MarkovBars + 1, price) < MarkovBars + 1)
   {
      Print("Insufficient historical data for Markov matrix: ", Bars(_Symbol, _Period), " bars available, ", MarkovBars + 1, " required");
      return;
   }

   int counts[3][3];
   ArrayInitialize(counts, 0);
   for(int i = 1; i < MarkovBars; i++)
   {
      MARKET_STATE prevState = GetStateFromPriceChange(price[i] - price[i + 1]);
      MARKET_STATE currState = GetStateFromPriceChange(price[i - 1] - price[i]);
      counts[prevState][currState]++;
   }

   for(int i = 0; i < 3; i++)
   {
      int total = counts[i][0] + counts[i][1] + counts[i][2];
      if(total > 0)
      {
         markovMatrix[i][0] = (double)counts[i][0] / total;
         markovMatrix[i][1] = (double)counts[i][1] / total;
         markovMatrix[i][2] = (double)counts[i][2] / total;
      }
      else
      {
         markovMatrix[i][0] = markovMatrix[i][1] = markovMatrix[i][2] = 0.33;
      }
   }
}

//+------------------------------------------------------------------+
//| Get Markov state                                                  |
//+------------------------------------------------------------------+
MARKET_STATE GetMarkovState()
{
   double price[];
   ArraySetAsSeries(price, true);
   ArrayResize(price, 2);
   if(CopyClose(_Symbol, _Period, 0, 2, price) < 2)
   {
      Print("Failed to copy price data for Markov state");
      return NEUTRAL;
   }
   return GetStateFromPriceChange(price[0] - price[1]);
}

//+------------------------------------------------------------------+
//| Get state from price change                                       |
//+------------------------------------------------------------------+
MARKET_STATE GetStateFromPriceChange(double change)
{
   if(change > MarkovBullish)
      return BULLISH;
   else if(change < MarkovBearish)
      return BEARISH;
   else
      return NEUTRAL;
}

//+------------------------------------------------------------------+
//| Detect bullish signal                                             |
//+------------------------------------------------------------------+
bool DetectBullishSignal(double &indicator[], string type)
{
   if(ArraySize(indicator) < 2) return false;
   if(type == "RSI")
      return indicator[0] < 20 && indicator[1] > indicator[0];
   if(type == "MACD")
      return indicator[0] > 0 && indicator[1] < indicator[0];
   if(type == "OBV")
      return indicator[0] > indicator[1];
   if(type == "VWAP")
      return indicator[0] < indicator[1];
   if(type == "Stochastic")
      return indicator[0] < 20 && indicator[1] > indicator[0];
   return false;
}

//+------------------------------------------------------------------+
//| Detect bearish signal                                             |
//+------------------------------------------------------------------+
bool DetectBearishSignal(double &indicator[], string type)
{
   if(ArraySize(indicator) < 2) return false;
   if(type == "RSI")
      return indicator[0] > 80 && indicator[1] < indicator[0];
   if(type == "MACD")
      return indicator[0] < 0 && indicator[1] > indicator[0];
   if(type == "OBV")
      return indicator[0] < indicator[1];
   if(type == "VWAP")
      return indicator[0] > indicator[1];
   if(type == "Stochastic")
      return indicator[0] > 80 && indicator[1] < indicator[0];
   return false;
}

//+------------------------------------------------------------------+
//| Detect bullish Bollinger Bands signal                             |
//+------------------------------------------------------------------+
bool DetectBullishBBSignal(double &upper[], double &lower[], double &price[])
{
   if(ArraySize(lower) < 2 || ArraySize(price) < 2) return false;
   return price[0] <= lower[0] && price[1] > lower[1];
}

//+------------------------------------------------------------------+
//| Detect bearish Bollinger Bands signal                             |
//+------------------------------------------------------------------+
bool DetectBearishBBSignal(double &upper[], double &lower[], double &price[])
{
   if(ArraySize(upper) < 2 || ArraySize(price) < 2) return false;
   return price[0] >= upper[0] && price[1] < upper[1];
}

//+------------------------------------------------------------------+
//| Detect bullish divergence                                         |
//+------------------------------------------------------------------+
bool DetectBullishDivergence(double &indicator[], double &stochastic[])
{
   if(ArraySize(indicator) < DivergenceBars || ArraySize(stochastic) < DivergenceBars) return false;
   double price[];
   ArraySetAsSeries(price, true);
   ArrayResize(price, DivergenceBars);
   if(CopyClose(_Symbol, _Period, 0, DivergenceBars, price) < DivergenceBars)
      return false;

   for(int i = 2; i < DivergenceBars - 1; i++)
   {
      if(indicator[i] < indicator[i + 1] && price[i] > price[i + 1] &&
         stochastic[i] < 20 && stochastic[i] > stochastic[i + 1])
         return true;
   }
   return false;
}

//+------------------------------------------------------------------+
//| Detect bearish divergence                                         |
//+------------------------------------------------------------------+
bool DetectBearishDivergence(double &indicator[], double &stochastic[])
{
   if(ArraySize(indicator) < DivergenceBars || ArraySize(stochastic) < DivergenceBars) return false;
   double price[];
   ArraySetAsSeries(price, true);
   ArrayResize(price, DivergenceBars);
   if(CopyClose(_Symbol, _Period, 0, DivergenceBars, price) < DivergenceBars)
      return false;

   for(int i = 2; i < DivergenceBars - 1; i++)
   {
      if(indicator[i] > indicator[i + 1] && price[i] < price[i + 1] &&
         stochastic[i] > 80 && stochastic[i] < stochastic[i + 1])
         return true;
   }
   return false;
}

//+------------------------------------------------------------------+
//| Detect bullish Bollinger Bands divergence                         |
//+------------------------------------------------------------------+
bool DetectBullishBBDivergence(double &lower[], double &stochastic[])
{
   if(ArraySize(lower) < DivergenceBars || ArraySize(stochastic) < DivergenceBars) return false;
   double price[];
   ArraySetAsSeries(price, true);
   ArrayResize(price, DivergenceBars);
   if(CopyClose(_Symbol, _Period, 0, DivergenceBars, price) < DivergenceBars)
      return false;

   for(int i = 2; i < DivergenceBars - 1; i++)
   {
      if(price[i] <= lower[i] && price[i + 1] > lower[i + 1] &&
         stochastic[i] < 20 && stochastic[i] > stochastic[i + 1])
         return true;
   }
   return false;
}

//+------------------------------------------------------------------+
//| Detect bearish Bollinger Bands divergence                         |
//+------------------------------------------------------------------+
bool DetectBearishBBDivergence(double &upper[], double &stochastic[])
{
   if(ArraySize(upper) < DivergenceBars || ArraySize(stochastic) < DivergenceBars) return false;
   double price[];
   ArraySetAsSeries(price, true);
   ArrayResize(price, DivergenceBars);
   if(CopyClose(_Symbol, _Period, 0, DivergenceBars, price) < DivergenceBars)
      return false;

   for(int i = 2; i < DivergenceBars - 1; i++)
   {
      if(price[i] >= upper[i] && price[i + 1] < upper[i + 1] &&
         stochastic[i] > 80 && stochastic[i] < stochastic[i + 1])
         return true;
   }
   return false;
}

//+------------------------------------------------------------------+
//| Update trailing stop                                              |
//+------------------------------------------------------------------+
void UpdateTrailingStop(ulong ticket)
{
   if(!PositionSelectByTicket(ticket))
      return;

   double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
   double trailingStop = TrailingStop * point;
   double currentPrice = SymbolInfoDouble(_Symbol, SYMBOL_BID);
   double sl = PositionGetDouble(POSITION_SL);

   MqlTradeRequest request;
   MqlTradeResult result;
   request.action = TRADE_ACTION_SLTP;
   request.position = ticket;
   request.symbol = _Symbol;

   if(PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY)
   {
      double newSL = currentPrice - trailingStop;
      if(newSL > sl && newSL > PositionGetDouble(POSITION_PRICE_OPEN))
      {
         request.sl = newSL;
         if(!OrderSend(request, result))
            Print("Trailing stop update failed for buy position, retcode=", result.retcode);
      }
   }
   else if(PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_SELL)
   {
      double newSL = currentPrice + trailingStop;
      if((newSL < sl || sl == 0) && newSL < PositionGetDouble(POSITION_PRICE_OPEN))
      {
         request.sl = newSL;
         if(!OrderSend(request, result))
            Print("Trailing stop update failed for sell position, retcode=", result.retcode);
      }
   }
}

//+------------------------------------------------------------------+
//| Close all positions                                               |
//+------------------------------------------------------------------+
void CloseAllPositions(ENUM_POSITION_TYPE type)
{
   MqlTradeRequest request;
   MqlTradeResult result;
   request.symbol = _Symbol;
   request.volume = LotSize;
   request.magic = MagicNumber;

   for(int i = PositionsTotal() - 1; i >= 0; i--)
   {
      ulong ticket = PositionGetTicket(i);
      if(PositionSelectByTicket(ticket) && PositionGetInteger(POSITION_MAGIC) == MagicNumber &&
         PositionGetInteger(POSITION_TYPE) == type)
      {
         MqlTick tick;
         SymbolInfoTick(_Symbol, tick);
         request.action = TRADE_ACTION_DEAL;
         request.type = type == POSITION_TYPE_BUY ? ORDER_TYPE_SELL : ORDER_TYPE_BUY;
         request.price = type == POSITION_TYPE_BUY ? tick.bid : tick.ask;
         for(int retry = 0; retry < 3; retry++)
         {
            if(OrderSend(request, result))
            {
               if(result.retcode == TRADE_RETCODE_DONE)
                  break;
               else if(result.retcode == TRADE_RETCODE_REQUOTE || result.retcode == TRADE_RETCODE_PRICE_OFF)
               {
                  SymbolInfoTick(_Symbol, tick);
                  request.price = type == POSITION_TYPE_BUY ? tick.bid : tick.ask;
                  continue;
               }
               else
               {
                  Print("Close position failed, retcode=", result.retcode);
                  break;
               }
            }
            Sleep(100);
         }
      }
   }
}