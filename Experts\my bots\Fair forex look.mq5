//+------------------------------------------------------------------+
//|                                                 fair look EA.mq5 |
//|                                  Copyright 2024, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "1.00"



///////////// FAIR GAPE VALUE DETECTOR INITIALS/////////////
#define FVG_Prefix "fvg Imbalance"
#define CLR_UP clrBlue
#define CLR_DOWN clrRed

input int minPts = 300;
input int FVG_Rec_Ext_Bars = 10;

string totalFVGS[];
int barINDICES[]; 

datetime barTIMEs[];
///////////// END OF FAIR GAPE VALUE DETECTOR INITIALS/////////////

/////////////START RANGE CONSIDULATION DETECTOR INITIALS/////////////
#define RANGE "Range Rectangle"

/////////////END  RANGE CONSIDULATION DETECTOR INITIALS/////////////

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit() {
 /////////////---------------beginning of FAIR VALURE GAP DETECTOR detector ------------------ /////////////////
 /////////////---------------beginning of FAIR VALURE GAP DETECTOR detector ------------------ /////////////////
  int visibleBars = (int)ChartGetInteger(0,CHART_VISIBLE_BARS);
  Print("Total Visible bars on chart = ",visibleBars);
  
  if(ObjectsTotal(0,0,OBJ_RECTANGLE)==0){
    Print("No FVGS Founds Resizing storage arrays to 0 now!!!");
    ArrayResize(totalFVGS,0);
    ArrayResize(barINDICES,0);
  }
  
  ObjectsDeleteAll(0,FVG_Prefix);
  
  for (int i=0; i<=visibleBars; i++){
      // Print("Bar index = ",i);
       double low0 = iLow(_Symbol,_Period,i);
       double high2 = iHigh(_Symbol,_Period,i+2);
       double gap_L0_H2 = NormalizeDouble((low0 - high2)/_Point,_Digits);
       
       double high0 = iHigh(_Symbol,_Period,i);
       double low2 = iLow(_Symbol,_Period,i+2);
       double gap_H0_L2 = NormalizeDouble((low2 - high0)/_Point,_Digits);
      
       bool FVG_UP = low0 > high2 && gap_L0_H2 > minPts;
       bool FVG_DOWN = low2 > high0 && gap_H0_L2 > minPts;
       
       if(FVG_UP || FVG_DOWN){
         Print("bars index with fvg = ",i+1);
         datetime time1 = iTime(_Symbol,_Period,i+1);
         double price1 = FVG_UP ? high2 : high0;
         datetime time2 = time1 + PeriodSeconds(_Period)*FVG_Rec_Ext_Bars;
         double price2 = FVG_UP ? low0 : low2;
         string fvgNAME = FVG_Prefix+"("+TimeToString(time1)+")";
         color fvgClr = FVG_UP ? CLR_UP : CLR_DOWN;
         CreateRec(fvgNAME,time1,price1,time2,price2,fvgClr);
         Print("Old ArraySize = ",ArraySize(totalFVGS));
         ArrayResize(totalFVGS,ArraySize(totalFVGS)+1);
          ArrayResize(barINDICES,ArraySize(barINDICES)+1);
         Print("New ArraySize = ",ArraySize(totalFVGS));
         totalFVGS[ArraySize(totalFVGS)-1] = fvgNAME;
         barINDICES[ArraySize(barINDICES)-1] = i+1;
         ArrayPrint(totalFVGS);
         ArrayPrint(barINDICES);
       }    
  }
  
    for (int i=ArraySize(totalFVGS)-1; i>=0; i--){
       string objName = totalFVGS[i];
       string fvgNAME = ObjectGetString(0,objName,OBJPROP_NAME);
       int barIndex = barINDICES[i];
       datetime timeSTART = (datetime)ObjectGetInteger(0,fvgNAME,OBJPROP_TIME,0);
       datetime timeEND = (datetime)ObjectGetInteger(0,fvgNAME,OBJPROP_TIME,1);
       double fvgLOW = ObjectGetDouble(0,fvgNAME,OBJPROP_PRICE,0);
       double fvgHIGH = ObjectGetDouble(0,fvgNAME,OBJPROP_PRICE,1);
       color fvgColor = (color)ObjectGetInteger(0,fvgNAME,OBJPROP_COLOR);
       
       
       //Print("FVG NAME = ",fvgNAME," >No: ",barIndex, " TS: ",timeSTART,"TE: ",
            // timeEND," LOW: ",fvgLOW," HIGH: ",fvgHIGH," CLR = ",fvgColor);
             
             for (int k=barIndex-1; k>=(barIndex-FVG_Rec_Ext_Bars); k--){
                datetime barTime = iTime(_Symbol,_Period,k);
                double barLow = iLow(_Symbol,_Period,k);
                double barHigh = iHigh(_Symbol,_Period,k);
               // Print("Bar No: ",k,">Time: ",barTime," >H: ",barHigh," >L: ",barLow);
                
                if (k==0){
                  Print("overflow detected @ bar fvg", fvgNAME);
                  UpdateRec(fvgNAME,timeSTART,fvgLOW,barTime,fvgHIGH);
                  break;
                }
              
                if((fvgColor == CLR_DOWN && barHigh > fvgHIGH) ||
                    (fvgColor == CLR_UP && barLow < fvgLOW)
               ){
                   Print("cut off @ bar no: ",k," of Time: ",barTime);
                   UpdateRec(fvgNAME,timeSTART,fvgLOW,barTime,fvgHIGH);
                   break;
              }
         }

     }
  
    ArrayResize(totalFVGS,0);
    ArrayResize(barINDICES,0);
 /////////////---------------END of FAIR VALURE GAP DETECTOR detector ------------------ /////////////////    
 
   return(INIT_SUCCEEDED);
  }
//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+




void OnDeinit(const int reason)
  {
//---
   
  }
   
  
//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+

void OnTick() {

 /////////////---------------BEGINING of FAIR VALURE GAP DETECTOR detector ------------------ /////////////////
 /////////////---------------BEGINING of FAIR VALURE GAP DETECTOR detector ------------------ /////////////////
       for (int i=0; i<=FVG_Rec_Ext_Bars; i++){ 
       double low0 = iLow(_Symbol,_Period,i+1);
       double high2 = iHigh(_Symbol,_Period,i+2+1);
       double gap_L0_H2 = NormalizeDouble((low0 - high2)/_Point,_Digits);
       
       double high0 = iHigh(_Symbol,_Period,i+1);
       double low2 = iLow(_Symbol,_Period,i+2+1);
       double gap_H0_L2 = NormalizeDouble((low2 - high0)/_Point,_Digits);
      
       bool FVG_UP = low0 > high2 && gap_L0_H2 > minPts;
       bool FVG_DOWN = low2 > high0 && gap_H0_L2 > minPts;
       
       if(FVG_UP || FVG_DOWN){
         datetime time1 = iTime(_Symbol,_Period,i+1+1);
         double price1 = FVG_UP ? high2 : high0;
         datetime time2 = time1 + PeriodSeconds(_Period)*FVG_Rec_Ext_Bars;
         double price2 = FVG_UP ? low0 : low2;
         string fvgNAME = FVG_Prefix+"("+TimeToString(time1)+")";
         color fvgClr = FVG_UP ? CLR_UP : CLR_DOWN;
         
         if (ObjectFind(0,fvgNAME) < 0){
         CreateRec(fvgNAME,time1,price1,time2,price2,fvgClr);
         Print("Old ArraySize = ",ArraySize(totalFVGS));
         ArrayResize(totalFVGS,ArraySize(totalFVGS)+1);
         ArrayResize(barTIMEs,ArraySize(barTIMEs)+1);
         Print("New ArraySize = ",ArraySize(totalFVGS));
         totalFVGS[ArraySize(totalFVGS)-1] = fvgNAME;
         barTIMEs[ArraySize(barTIMEs)-1] = time1;
         ArrayPrint(totalFVGS);
         ArrayPrint(barTIMEs);
       }       
     }
  }
  
  for (int j =ArraySize(totalFVGS)-1; j>=0; j--){
       bool fvgExist  = false;
       string objName = totalFVGS[j];
       string fvgNAME = ObjectGetString(0,objName,OBJPROP_NAME);
       double fvgLow = ObjectGetDouble(0,fvgNAME,OBJPROP_PRICE,0);
       double fvgHigh = ObjectGetDouble(0,fvgNAME,OBJPROP_PRICE,1);
       color fvgColor = (color)ObjectGetInteger(0,fvgNAME,OBJPROP_COLOR);
        
       for (int k=1; k<=FVG_Rec_Ext_Bars; k++){
           double barLow = iLow(_Symbol,_Period,k);
           double barHigh = iHigh(_Symbol,_Period,k);
           
           if(barHigh == fvgLow  || barLow == fvgLow){
             // Print("Found: ",fvgNAME," @bar ",k);
              fvgExist = true;
              break;
           }
       }
       //  Print("Existence of ",fvgNAME," = ",fvgExist);
       
       if (fvgExist == false){
        bool removeName =  ArrayRemove(totalFVGS,0,1);
        bool removeTime =  ArrayRemove(barTIMEs,0,1);
          if (removeName){
             Print("Success removing the FVG DATA from the arrays. New Data as Below"); 
             Print("FVGs: ",ArraySize(totalFVGS), " TIMEs: ",ArraySize(barTIMEs));
             ArrayPrint(totalFVGS);
             ArrayPrint(barTIMEs);
          }
       }
       
       double Ask = NormalizeDouble(SymbolInfoDouble(_Symbol,SYMBOL_ASK),_Digits);
       double Bid = NormalizeDouble(SymbolInfoDouble(_Symbol,SYMBOL_BID),_Digits);
     
       if (fvgColor == CLR_DOWN && Bid > fvgHigh){
          Print("SELL SIGNAL FOR (",fvgNAME,")Now @ ",Bid);     
       }  
       else  if (fvgColor == CLR_UP && Ask < fvgLow){
          Print("BUY SIGNAL FOR (",fvgNAME,")Now @ ",Ask);     
       }
       
  } 
  /////////////---------------END of FAIR VALURE GAP DETECTOR detector ------------------ /////////////////
  

  //---Candle pattern detector call in on tick---//
      getHammerSignal(0.05,0.7);  
      getEngulfingSignal();
      getStarSignal(0.8);
  //---END OF Candle pattern detector call in on tick---//
  
  //---RANGE CONSALIDATION detector call in on tick---// 
    consalidationDetector();
  
}



 /////////////---------------BEGING of FAIR VALURE GAP DETECTOR detector OBJECT CREATION ------------------ /////////////////
 /////////////---------------BEGING of FAIR VALURE GAP DETECTOR detector OBJECT CREATION------------------ /////////////////

void CreateRec(string objName,datetime time1, double price1,
               datetime time2, double price2,color clr){
   if (ObjectFind(0,objName) < 0){
   ObjectCreate(0,objName,OBJ_RECTANGLE,0,time1,price1,time2,price2);
 
   ObjectSetInteger(0,objName,OBJPROP_TIME,0,time1);
   ObjectSetDouble(0,objName,OBJPROP_PRICE,0,price1);
   ObjectSetInteger(0,objName,OBJPROP_TIME,1,time2);
   ObjectSetDouble(0,objName,OBJPROP_PRICE,1,price2);
   ObjectSetInteger(0,objName,OBJPROP_COLOR,clr);
   ObjectSetInteger(0,objName,OBJPROP_FILL,true);
   ObjectSetInteger(0,objName,OBJPROP_BACK,false);
   
   ChartRedraw(0);
   }
}


void UpdateRec(string objName,datetime time1, double price1,
               datetime time2, double price2){
   if (ObjectFind(0,objName) >= 0){
   ObjectSetInteger(0,objName,OBJPROP_TIME,0,time1);
   ObjectSetDouble(0,objName,OBJPROP_PRICE,0,price1);
   ObjectSetInteger(0,objName,OBJPROP_TIME,1,time2);
   ObjectSetDouble(0,objName,OBJPROP_PRICE,1,price2);
   
   ChartRedraw(0);
   }
}


 /////////////---------------END of FAIR VALURE GAP DETECTOR detector OBJECT CREATION ------------------ /////////////////






 /////////////---------------beginning of candle pattern detector ------------------ /////////////////
 /////////////---------------beginning of candle pattern detector ------------------ /////////////////

int getStarSignal(double maxMiddleCandleRatio){
    datetime time = iTime(_Symbol,PERIOD_CURRENT,1);
     
    double high1 = iHigh(_Symbol,PERIOD_CURRENT,1);
    double low1 = iLow(_Symbol,PERIOD_CURRENT,1);
    double open1 = iOpen(_Symbol,PERIOD_CURRENT,1);
    double close1 = iClose(_Symbol,PERIOD_CURRENT,1);
    
    double high2 = iHigh(_Symbol,PERIOD_CURRENT,2);
    double low2 = iLow(_Symbol,PERIOD_CURRENT,2);
    double open2 = iOpen(_Symbol,PERIOD_CURRENT,2);
    double close2 = iClose(_Symbol,PERIOD_CURRENT,2);
   
    double high3 = iHigh(_Symbol,PERIOD_CURRENT,3);
    double low3 = iLow(_Symbol,PERIOD_CURRENT,3);
    double open3 = iOpen(_Symbol,PERIOD_CURRENT,3);
    double close3 = iClose(_Symbol,PERIOD_CURRENT,3); 
    
    double size1 = high1 - low1;
    double size2 = high2 - low2;
    double size3 = high3 - low3;
    
    if(open1 < close1){
      if(open3 > close3){
         if(size2 < size1 * maxMiddleCandleRatio && size2 < size3 * maxMiddleCandleRatio){
            createObj(time,low1,200,1,clrBlue,"MrngStr");
            return 1;    
        }
      } 
    }
    
     if(open1 > close1){
      if(open3 < close3){
         if(size2 < size1 * maxMiddleCandleRatio && size2 < size3 * maxMiddleCandleRatio){
            createObj(time,high1,201,-1,clrRed,"EvngStr");
            return 1;    
        }
      }
    }
    
    return 0;
  }



int getEngulfingSignal(){  
    datetime time = iTime(_Symbol,PERIOD_CURRENT,1);
     
    double high1 = iHigh(_Symbol,PERIOD_CURRENT,1);
    double low1 = iLow(_Symbol,PERIOD_CURRENT,1);
    double open1 = iOpen(_Symbol,PERIOD_CURRENT,1);
    double close1 = iClose(_Symbol,PERIOD_CURRENT,1);
    
    double high2 = iHigh(_Symbol,PERIOD_CURRENT,2);
    double low2 = iLow(_Symbol,PERIOD_CURRENT,2);
    double open2 = iOpen(_Symbol,PERIOD_CURRENT,2);
    double close2 = iClose(_Symbol,PERIOD_CURRENT,2);
    
    //bullish engulfing formation
    
    if(open1 < close1){
      if (open2 > close2){
         if(high1 > high2 && low1 < low2){
            if(close1 > open2 && open1 < close2){
               createObj(time,low1,217,1,clrBlue,"ENGLF");
                return 1;
             }
           }    
         }
       }
    
     //berish engulfing formation
     if(open1 > close1){
      if (open2 < close2){
         if(high1 > high2 && low1 < low2){
            if(close1 < open2 && open1 > close2){
                createObj(time,high1,218,-1,clrRed,"ENGLF");
                  return -1;
              }
            }    
          }
        }
          
    return 0;
}




int getHammerSignal(double maxRatioShortShadow, double minRatioLongShadow){  
    datetime time = iTime(_Symbol,PERIOD_CURRENT,1);
     
    double high = iHigh(_Symbol,PERIOD_CURRENT,1);
    double low = iLow(_Symbol,PERIOD_CURRENT,1);
    double open = iOpen(_Symbol,PERIOD_CURRENT,1);
    double close = iClose(_Symbol,PERIOD_CURRENT,1);
      
    double candleSize = high - low; 
    
    //green buy hammer formation
    if(open < close){
       if(high - close < candleSize * maxRatioShortShadow){
          if(open - low > candleSize * minRatioLongShadow){
            createObj(time,low,233,1,clrBlue,"HMR");
             return 1;
          }
       }
    
    }
    
    
        //red hammer buy formation
    if(open > close){
       if(high - open < candleSize * maxRatioShortShadow){
          if(close - low > candleSize * minRatioLongShadow){
             createObj(time,low,233,1,clrBlue,"HMR");
             return 1;
          }
       }
    
    } 
    
    //green hammer sell  formation
    if(open < close){
       if(open - low < candleSize * maxRatioShortShadow){
          if(high - close > candleSize * minRatioLongShadow){
            createObj(time,high,234,-1,clrRed,"HMR");
             return -1;
          }
       }
    
    }
    
     //red hammer sell  formation
    if(open > close){
       if(close - low < candleSize * maxRatioShortShadow){
          if(high - open > candleSize * minRatioLongShadow){
            createObj(time,high,234,-1,clrRed,"HMR");
             return -1;
          }
       }
    
    }
    
    
   
    return 0;
}
 
 
 
 
void createObj(datetime time, double price, int arrowCode, int direction,  color clr, string txt){
    string objName = "";
    StringConcatenate(objName,"signal@", time, "at", DoubleToString(price,_Digits), "(",arrowCode,")");
    if(ObjectCreate(0,objName,OBJ_ARROW,0,time,price)){
       ObjectSetInteger(0,objName,OBJPROP_ARROWCODE,arrowCode);
       ObjectSetInteger(0,objName,OBJPROP_COLOR,clr);
       if(direction > 0)  ObjectSetInteger(0,objName,OBJPROP_ANCHOR,ANCHOR_TOP);     
       if(direction < 0)  ObjectSetInteger(0,objName,OBJPROP_ANCHOR,ANCHOR_BOTTOM);      
       
    }
    string objNameDesc = objName + txt;
    if(ObjectCreate(0,objNameDesc,OBJ_TEXT,0,time,price)){
      ObjectSetString(0,objNameDesc,OBJPROP_TEXT,""+txt) ;
      ObjectSetInteger(0,objNameDesc ,OBJPROP_COLOR,clr);
      if(direction > 0)  ObjectSetInteger(0,objNameDesc,OBJPROP_ANCHOR,ANCHOR_TOP);     
       if(direction < 0)  ObjectSetInteger(0,objNameDesc,OBJPROP_ANCHOR,ANCHOR_BOTTOM);      
       
   }
}

 /////////////---------------END of candle pattern detector ------------------ /////////////////
 
 
 
 
 
 
 /////////////---------------beginning of RANGE CONSILIDATION detector ------------------ /////////////////
 /////////////---------------beginning of RANGE CONSILIDATION detector ------------------ /////////////////

void consalidationDetector(){

 int
        indexHighestBar = iHighest(_Symbol,_Period,MODE_HIGH,30,1),
        indexLowestBar = iLowest(_Symbol,_Period,MODE_LOW,30,1); 
        
     double
        rangeHighPrice = iHigh(_Symbol,_Period,indexHighestBar),
        rangeLowPrice = iLow(_Symbol,_Period,indexLowestBar);
      
     datetime
        RangeStartTime = iTime(_Symbol,_Period,30),
        RangeEndTime = iTime(_Symbol,_Period,0);
        
    double currentRangeHeight = NormalizeDouble(rangeHighPrice - rangeLowPrice,_Digits);         
   
    bool isInRange = currentRangeHeight < 500*_Point;
        
    if(isInRange){
       drawRange(RangeStartTime,rangeHighPrice,RangeEndTime,rangeLowPrice);
     }
     
  }
  
  
void drawRange(datetime time1, double price1, datetime time2, double price2){
   //ObjectDelete(0,RANGE);
  // ObjectCreate(0,RANGE,OBJ_RECTANGLE,0,time1,price1,time2,price2);
   int found = ObjectFind(0,RANGE);
   if(found < 0){
      ObjectCreate(0,RANGE,OBJ_RECTANGLE,0,time1,price1,time2,price2);
   }
   else {
      ObjectSetInteger(0,RANGE,OBJPROP_TIME,0,time1);
      ObjectSetDouble(0,RANGE,OBJPROP_PRICE,0,price1);
      ObjectSetInteger(0,RANGE,OBJPROP_TIME,1,time2);
      ObjectSetDouble(0,RANGE,OBJPROP_PRICE,1,price2);
      ObjectSetInteger(0,RANGE,OBJPROP_FILL,true);
      ObjectSetInteger(0,RANGE,OBJPROP_COLOR,clrGoldenrod);
      ObjectSetInteger(0,RANGE,OBJPROP_WIDTH,7);
      ObjectSetInteger(0,RANGE,OBJPROP_STYLE,STYLE_SOLID);
      ObjectSetInteger(0,RANGE,OBJPROP_BACK,true);
  
   }
   
   }
   
    
 /////////////---------------END of RANGE CONSILIDATION detector ------------------ /////////////////
 /////////////---------------END of RANGE CONSILIDATION detector ------------------ /////////////////